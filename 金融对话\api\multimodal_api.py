"""
多模态图像检索API端点
提供PDF图像提取、索引、搜索和分析的API接口
"""
import os
import sys
import time
from pathlib import Path
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, UploadFile, File, HTTPException, Form, Query
from pydantic import BaseModel
from loguru import logger

# 添加父目录到Python路径
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

try:
    from rag.multimodal_retrieval import MultimodalImageRetriever
    MULTIMODAL_AVAILABLE = True
except ImportError:
    MULTIMODAL_AVAILABLE = False
    logger.warning("多模态功能不可用，请安装相关依赖")

# 创建路由器
router = APIRouter(prefix="/multimodal", tags=["多模态图像检索"])

# 全局多模态检索器实例
multimodal_retriever = None

def get_multimodal_retriever():
    """获取多模态检索器实例"""
    global multimodal_retriever
    if multimodal_retriever is None:
        multimodal_retriever = MultimodalImageRetriever()
        if not multimodal_retriever.initialize():
            raise HTTPException(status_code=500, detail="多模态检索器初始化失败")
    return multimodal_retriever

# Pydantic模型
class ImageSearchResult(BaseModel):
    image_id: str
    pdf_name: str
    page_number: int
    image_index: int
    description: str
    image_type: str
    similarity: float
    text_score: Optional[float] = None
    final_score: Optional[float] = None
    width: int
    height: int

class ImageSearchResponse(BaseModel):
    results: List[ImageSearchResult]
    query: str
    total_results: int
    search_time_ms: float
    search_type: str

class ImageStatisticsResponse(BaseModel):
    total_files: int
    total_images: int
    image_types: Dict[str, int]
    size_distribution: Dict[str, int]
    pages_with_images: Dict[str, int]

class ImageAnalysisResponse(BaseModel):
    basic_info: Dict[str, Any]
    description: str
    image_type: str
    color_analysis: Dict[str, Any]
    complexity_score: float
    financial_relevance: Dict[str, Any]

class ImageExtractionResponse(BaseModel):
    success: bool
    message: str
    total_images: int
    file_hash: str

@router.post("/extract/{file_hash}", response_model=ImageExtractionResponse)
async def extract_images_from_pdf(
    file_hash: str,
    pdf_path: str = Form(..., description="PDF文件路径")
):
    """
    从PDF中提取并索引图像
    """
    try:
        retriever = get_multimodal_retriever()
        
        # 检查PDF文件是否存在
        if not os.path.exists(pdf_path):
            raise HTTPException(status_code=404, detail="PDF文件不存在")
        
        # 提取并索引图像
        success = retriever.index_images(pdf_path, file_hash)
        
        if success:
            # 获取提取的图像数量
            file_info = retriever.image_index.get(file_hash, {})
            total_images = file_info.get("total_images", 0)
            
            return ImageExtractionResponse(
                success=True,
                message=f"成功提取并索引 {total_images} 个图像",
                total_images=total_images,
                file_hash=file_hash
            )
        else:
            return ImageExtractionResponse(
                success=False,
                message="图像提取失败或未找到图像",
                total_images=0,
                file_hash=file_hash
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"提取图像失败: {e}")
        raise HTTPException(status_code=500, detail=f"提取失败: {str(e)}")

@router.get("/search/text", response_model=ImageSearchResponse)
async def search_images_by_text(
    query: str = Query(..., description="文本查询"),
    top_k: int = Query(5, description="返回结果数量", ge=1, le=20)
):
    """
    基于文本查询搜索图像
    """
    try:
        start_time = time.time()
        
        retriever = get_multimodal_retriever()
        results = retriever.search_images_by_text(query, top_k)
        
        search_time = (time.time() - start_time) * 1000
        
        image_results = [ImageSearchResult(**result) for result in results]
        
        return ImageSearchResponse(
            results=image_results,
            query=query,
            total_results=len(image_results),
            search_time_ms=round(search_time, 2),
            search_type="text"
        )
        
    except Exception as e:
        logger.error(f"文本搜索图像失败: {e}")
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")

@router.post("/search/image", response_model=ImageSearchResponse)
async def search_images_by_image(
    query_image: UploadFile = File(..., description="查询图像"),
    top_k: int = Form(5, description="返回结果数量")
):
    """
    基于图像查询搜索相似图像
    """
    try:
        start_time = time.time()
        
        # 检查文件类型
        if not query_image.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="只支持图像文件")
        
        # 保存临时查询图像
        temp_dir = Path("temp")
        temp_dir.mkdir(exist_ok=True)
        temp_image_path = temp_dir / f"query_{int(time.time())}_{query_image.filename}"
        
        with open(temp_image_path, "wb") as buffer:
            content = await query_image.read()
            buffer.write(content)
        
        try:
            retriever = get_multimodal_retriever()
            results = retriever.search_images_by_image(str(temp_image_path), top_k)
            
            search_time = (time.time() - start_time) * 1000
            
            # 转换结果格式
            image_results = []
            for result in results:
                image_result = ImageSearchResult(
                    image_id=result["image_id"],
                    pdf_name=result["pdf_name"],
                    page_number=result["page_number"],
                    image_index=result["image_index"],
                    description=result["description"],
                    image_type=result["image_type"],
                    similarity=result["similarity"],
                    width=result["width"],
                    height=result["height"]
                )
                image_results.append(image_result)
            
            return ImageSearchResponse(
                results=image_results,
                query=f"图像查询: {query_image.filename}",
                total_results=len(image_results),
                search_time_ms=round(search_time, 2),
                search_type="image"
            )
            
        finally:
            # 清理临时文件
            if temp_image_path.exists():
                temp_image_path.unlink()
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"图像搜索失败: {e}")
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")

@router.get("/statistics", response_model=ImageStatisticsResponse)
async def get_image_statistics():
    """
    获取图像统计信息
    """
    try:
        retriever = get_multimodal_retriever()
        stats = retriever.get_image_statistics()
        
        return ImageStatisticsResponse(**stats)
        
    except Exception as e:
        logger.error(f"获取图像统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")

@router.get("/analyze/{image_id}", response_model=ImageAnalysisResponse)
async def analyze_image(image_id: str):
    """
    分析特定图像的内容
    """
    try:
        retriever = get_multimodal_retriever()
        analysis = retriever.analyze_image_content(image_id)
        
        if "error" in analysis:
            raise HTTPException(status_code=404, detail=analysis["error"])
        
        return ImageAnalysisResponse(**analysis)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"分析图像失败: {e}")
        raise HTTPException(status_code=500, detail=f"分析失败: {str(e)}")

@router.get("/image/{image_id}")
async def get_image_file(image_id: str):
    """
    获取图像文件
    """
    try:
        retriever = get_multimodal_retriever()
        
        # 查找图像信息
        image_info = None
        for file_info in retriever.image_index.values():
            for img in file_info.get("images", []):
                if img.get("image_id") == image_id:
                    image_info = img
                    break
            if image_info:
                break
        
        if not image_info:
            raise HTTPException(status_code=404, detail="图像不存在")
        
        image_path = image_info.get("image_path")
        if not os.path.exists(image_path):
            raise HTTPException(status_code=404, detail="图像文件不存在")
        
        from fastapi.responses import FileResponse
        return FileResponse(
            image_path,
            media_type="image/png",
            filename=f"{image_id}.png"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取图像文件失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取失败: {str(e)}")

@router.get("/types")
async def get_image_types():
    """
    获取支持的图像类型和关键词
    """
    try:
        retriever = get_multimodal_retriever()
        
        return {
            "financial_keywords": retriever.financial_image_keywords,
            "supported_formats": ["PNG", "JPEG", "JPG", "GIF", "BMP"],
            "search_types": ["text", "image"],
            "classification_categories": [
                "柱状图", "折线图", "饼图", "散点图", "K线图",
                "财务报表", "收入图表", "利润分析", "现金流", "市场数据",
                "增长趋势", "市场份额", "客户分析", "产品分布"
            ]
        }
        
    except Exception as e:
        logger.error(f"获取图像类型失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取失败: {str(e)}")

@router.delete("/clear/{file_hash}")
async def clear_image_index(file_hash: str):
    """
    清除特定文件的图像索引
    """
    try:
        retriever = get_multimodal_retriever()
        
        if file_hash not in retriever.image_index:
            raise HTTPException(status_code=404, detail="文件索引不存在")
        
        # 删除图像文件
        file_info = retriever.image_index[file_hash]
        for image_info in file_info.get("images", []):
            image_path = image_info.get("image_path")
            if image_path and os.path.exists(image_path):
                os.remove(image_path)
        
        # 删除索引
        del retriever.image_index[file_hash]
        retriever._save_image_index()
        
        return {"message": f"成功清除文件 {file_hash} 的图像索引"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"清除图像索引失败: {e}")
        raise HTTPException(status_code=500, detail=f"清除失败: {str(e)}")

@router.post("/batch/test-parsing")
async def test_batch_parsing(
    test_response: str = Form(..., description="测试用的API响应文本"),
    image_count: int = Form(3, description="图像数量")
):
    """
    测试批量解析功能
    """
    try:
        from llm.llm_service import LLMService

        llm_service = LLMService()

        # 创建模拟图像数据
        images_data = [
            {"image_id": f"test_image_{i+1}", "image_base64": f"fake_base64_{i+1}"}
            for i in range(image_count)
        ]

        # 测试解析
        results = llm_service._parse_batch_results(test_response, images_data)

        # 分析结果
        success_count = sum(1 for r in results if r.get("success", False))
        parse_methods = [r.get("parse_method", "standard") for r in results]

        return {
            "test_response": test_response[:200] + "..." if len(test_response) > 200 else test_response,
            "expected_count": image_count,
            "actual_count": len(results),
            "success_count": success_count,
            "success_rate": success_count / len(results) * 100 if results else 0,
            "parse_methods": parse_methods,
            "results": [
                {
                    "image_id": r.get("image_id"),
                    "success": r.get("success"),
                    "description_length": len(r.get("description", "")),
                    "parse_method": r.get("parse_method", "standard"),
                    "description_preview": r.get("description", "")[:100] + "..." if len(r.get("description", "")) > 100 else r.get("description", "")
                }
                for r in results
            ]
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"测试失败: {str(e)}")

@router.get("/batch/stats")
async def get_batch_processing_stats():
    """
    获取批量处理统计信息
    """
    try:
        return {
            "batch_processing_enabled": True,
            "default_batch_size": 5,  # 用户已改为5
            "supported_parse_methods": [
                "standard",
                "paragraph",
                "sentence",
                "fallback"
            ],
            "optimization_features": [
                "多策略解析",
                "强化提示词",
                "智能格式匹配",
                "容错处理"
            ],
            "performance_tips": [
                "建议批次大小：3-5张图像",
                "确保图像质量清晰",
                "提供详细的上下文信息",
                "使用专业的金融术语"
            ]
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")

# 健康检查端点
@router.get("/health")
async def health_check():
    """
    多模态服务健康检查
    """
    try:
        retriever = get_multimodal_retriever()
        stats = retriever.get_image_statistics()
        
        return {
            "status": "healthy",
            "multimodal_retriever": "initialized",
            "clip_model": "loaded" if retriever.clip_model is not None else "not_loaded",
            "blip_model": "loaded" if retriever.blip_model is not None else "not_loaded",
            "total_images": stats.get("total_images", 0),
            "device": retriever.device
        }
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return {
            "status": "unhealthy",
            "error": str(e)
        }
