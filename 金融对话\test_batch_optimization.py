"""
测试优化后的批量图像描述功能
"""
import os
import sys
import time
from pathlib import Path

# 添加父目录到Python路径
parent_dir = Path(__file__).parent
sys.path.insert(0, str(parent_dir))

from llm.llm_service import LLMService
from idconfig.config import Config

def test_batch_parsing():
    """测试批量解析功能"""
    print("=== 测试批量解析功能 ===")
    
    llm_service = LLMService()
    
    # 模拟API返回的不同格式
    test_cases = [
        {
            "name": "标准格式",
            "response": """图像1：这是一个柱状图，显示了公司过去五年的营收增长情况。
图像2：这是一个饼图，展示了不同业务部门的收入占比。
图像3：这是一个折线图，反映了股价的变化趋势。""",
            "expected_count": 3
        },
        {
            "name": "不规范格式",
            "response": """第一张图像显示了财务数据的柱状图表现。

第二张图片是关于市场份额的饼图分析。

第三个图表展示了时间序列的趋势变化。""",
            "expected_count": 3
        },
        {
            "name": "混合格式",
            "response": """图像1：营收柱状图显示增长趋势。这个图表包含了详细的数据分析。
2. 市场份额饼图：展示各部门占比情况，其中销售部门占据最大份额。
图3：股价走势图反映了市场波动，整体呈现上升趋势。""",
            "expected_count": 3
        }
    ]
    
    # 模拟图像数据
    images_data = [
        {"image_id": "test_1", "image_base64": "fake_base64_1"},
        {"image_id": "test_2", "image_base64": "fake_base64_2"},
        {"image_id": "test_3", "image_base64": "fake_base64_3"}
    ]
    
    for test_case in test_cases:
        print(f"\n--- 测试 {test_case['name']} ---")
        print(f"原始响应: {test_case['response'][:100]}...")
        
        results = llm_service._parse_batch_results(test_case['response'], images_data)
        
        print(f"解析结果数量: {len(results)}")
        print(f"期望数量: {test_case['expected_count']}")
        
        for i, result in enumerate(results):
            print(f"  图像{i+1}: {result.get('image_id')} - 成功: {result.get('success')}")
            if result.get('parse_method'):
                print(f"    解析方法: {result.get('parse_method')}")
            print(f"    描述: {result.get('description', '')[:80]}...")
        
        success_rate = len([r for r in results if r.get('success')]) / len(results) * 100
        print(f"成功率: {success_rate:.1f}%")

def test_prompt_generation():
    """测试提示词生成"""
    print("\n=== 测试提示词生成 ===")
    
    llm_service = LLMService()
    
    # 模拟图像数据
    images_data = [
        {
            "image_id": "img_1",
            "image_base64": "fake_base64_1",
            "context_info": {
                "pdf_name": "财务报告.pdf",
                "page_number": 15,
                "page_text": "本页展示了公司2023年第三季度的营收数据，包括各业务部门的表现..."
            }
        },
        {
            "image_id": "img_2", 
            "image_base64": "fake_base64_2",
            "context_info": {
                "pdf_name": "市场分析.pdf",
                "page_number": 8,
                "page_text": "市场份额分析显示，我们在核心业务领域保持领先地位..."
            }
        }
    ]
    
    prompt = llm_service._build_batch_prompt(
        "请详细分析这些图像的内容", 
        images_data
    )
    
    print("生成的批量提示词:")
    print("=" * 50)
    print(prompt)
    print("=" * 50)
    
    # 检查提示词是否包含关键元素
    checks = [
        ("图像数量", str(len(images_data)) in prompt),
        ("格式要求", "图像1：" in prompt and "图像2：" in prompt),
        ("上下文信息", "财务报告.pdf" in prompt),
        ("强化要求", "重要" in prompt or "严格" in prompt),
    ]
    
    print("\n提示词质量检查:")
    for check_name, passed in checks:
        status = "✅" if passed else "❌"
        print(f"{status} {check_name}: {'通过' if passed else '未通过'}")

if __name__ == "__main__":
    # 检查配置
    config = Config()
    if not config.SILICONFLOW2_API_KEY:
        print("⚠️  硅基流动2 API密钥未配置，只能测试解析功能")
    
    # 运行测试
    test_batch_parsing()
    test_prompt_generation()
    
    print("\n=== 测试完成 ===")
    print("优化要点:")
    print("1. 多策略解析：标准格式 -> 段落格式 -> 句子格式 -> 备用方案")
    print("2. 强化提示词：明确要求输出格式和图像数量")
    print("3. 智能匹配：支持多种编号格式和分隔符")
    print("4. 容错处理：确保每张图像都有描述结果")
