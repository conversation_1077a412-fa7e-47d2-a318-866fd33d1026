"""
测试批量图像描述生成功能
"""
import os
import sys
import time
from pathlib import Path
from PIL import Image
import base64

# 添加父目录到Python路径
parent_dir = Path(__file__).parent
sys.path.insert(0, str(parent_dir))

from llm.llm_service import LLMService
from rag.multimodal_retrieval import MultimodalImageRetriever
from idconfig.config import Config

def test_batch_image_description():
    """测试批量图像描述生成"""
    print("=== 测试批量图像描述生成功能 ===")
    
    # 初始化服务
    config = Config()
    llm_service = LLMService()
    
    # 检查硅基流动2配置
    if not config.SILICONFLOW2_API_KEY:
        print("❌ 硅基流动2 API密钥未配置")
        return
    
    print("✅ 硅基流动2 API密钥已配置")
    
    # 查找测试图像
    image_dir = Path("data/images")
    if not image_dir.exists():
        print("❌ 图像目录不存在")
        return
    
    # 获取前几张图像进行测试
    image_files = list(image_dir.glob("*.jpeg"))[:3]  # 测试3张图像
    if not image_files:
        image_files = list(image_dir.glob("*.jpg"))[:3]
    if not image_files:
        image_files = list(image_dir.glob("*.png"))[:3]
    
    if not image_files:
        print("❌ 没有找到测试图像")
        return
    
    print(f"✅ 找到 {len(image_files)} 张测试图像")
    
    # 准备批量数据
    images_data = []
    for i, image_file in enumerate(image_files):
        try:
            # 读取图像并转换为base64
            with open(image_file, "rb") as f:
                image_data = f.read()
                image_base64 = base64.b64encode(image_data).decode('utf-8')
            
            images_data.append({
                "image_base64": image_base64,
                "image_id": f"test_image_{i+1}",
                "context_info": {
                    "pdf_name": "测试文档",
                    "page_number": i + 1,
                    "page_text": f"这是第{i+1}页的测试内容，包含财务数据和图表分析。"
                }
            })
            print(f"✅ 准备图像 {i+1}: {image_file.name}")
            
        except Exception as e:
            print(f"❌ 准备图像失败 {image_file}: {e}")
    
    if not images_data:
        print("❌ 没有成功准备任何图像数据")
        return
    
    print(f"\n=== 开始批量处理 {len(images_data)} 张图像 ===")
    
    # 测试批量API调用
    start_time = time.time()
    
    try:
        result = llm_service.call_siliconflow2_vision_batch(
            images_data=images_data,
            prompt="请详细分析这些图像，重点关注金融相关内容",
            temperature=0.3
        )
        
        processing_time = time.time() - start_time
        
        if result.get("success"):
            print(f"✅ 批量处理成功，耗时 {processing_time:.2f} 秒")
            
            results = result.get("results", [])
            print(f"✅ 获得 {len(results)} 个结果")
            
            # 显示结果
            for i, res in enumerate(results):
                print(f"\n--- 图像 {i+1} 结果 ---")
                print(f"图像ID: {res.get('image_id')}")
                print(f"成功: {res.get('success')}")
                if res.get('success'):
                    description = res.get('description', '')
                    print(f"描述: {description[:200]}...")
                else:
                    print(f"错误: {res.get('error')}")
            
            # 显示原始响应（截取）
            raw_response = result.get("raw_response", "")
            print(f"\n--- 原始API响应（前500字符） ---")
            print(raw_response[:500] + "..." if len(raw_response) > 500 else raw_response)
            
        else:
            print(f"❌ 批量处理失败: {result.get('error')}")
            
    except Exception as e:
        print(f"❌ 批量处理异常: {e}")
    
    print(f"\n=== 测试完成 ===")

def test_multimodal_retriever_batch():
    """测试多模态检索器的批量功能"""
    print("\n=== 测试多模态检索器批量功能 ===")
    
    try:
        # 初始化多模态检索器
        retriever = MultimodalImageRetriever()
        if not retriever.initialize():
            print("❌ 多模态检索器初始化失败")
            return
        
        print("✅ 多模态检索器初始化成功")
        
        # 查找测试图像
        image_dir = Path("data/images")
        image_files = list(image_dir.glob("*.jpeg"))[:2]  # 测试2张图像
        
        if not image_files:
            print("❌ 没有找到测试图像")
            return
        
        # 准备批量数据
        images_with_context = []
        for i, image_file in enumerate(image_files):
            try:
                image = Image.open(image_file)
                images_with_context.append({
                    "image": image,
                    "image_id": f"retriever_test_{i+1}",
                    "context_info": {
                        "pdf_name": "测试文档",
                        "page_number": i + 1,
                        "page_text": f"这是第{i+1}页的财务报表内容。"
                    }
                })
                print(f"✅ 准备图像 {i+1}: {image_file.name}")
            except Exception as e:
                print(f"❌ 准备图像失败 {image_file}: {e}")
        
        if not images_with_context:
            print("❌ 没有成功准备任何图像数据")
            return
        
        # 测试批量描述生成
        start_time = time.time()
        
        results = retriever.generate_batch_image_descriptions(
            images_with_context=images_with_context,
            batch_size=2
        )
        
        processing_time = time.time() - start_time
        
        print(f"✅ 批量描述生成完成，耗时 {processing_time:.2f} 秒")
        print(f"✅ 获得 {len(results)} 个结果")
        
        # 显示结果
        for i, result in enumerate(results):
            print(f"\n--- 图像 {i+1} 结果 ---")
            print(f"图像ID: {result.get('image_id')}")
            print(f"成功: {result.get('success')}")
            print(f"方法: {result.get('method', 'unknown')}")
            if result.get('success'):
                description = result.get('description', '')
                print(f"描述: {description[:200]}...")
            else:
                print(f"错误: {result.get('error')}")
        
        success_count = sum(1 for r in results if r.get('success'))
        print(f"\n✅ 成功处理 {success_count}/{len(results)} 张图像")
        
    except Exception as e:
        print(f"❌ 多模态检索器批量测试失败: {e}")

if __name__ == "__main__":
    # 测试LLM服务的批量功能
    test_batch_image_description()
    
    # 测试多模态检索器的批量功能
    test_multimodal_retriever_batch()
