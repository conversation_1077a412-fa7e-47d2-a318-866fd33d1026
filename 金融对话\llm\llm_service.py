"""
在线大模型调用服务
支持多种在线大模型服务
"""
import openai
import requests
import json
import hashlib
import hmac
import base64
import time
import urllib.parse
import pickle
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, Any, List, Optional, Tuple
from loguru import logger
import sys
from pathlib import Path

# 添加父目录到Python路径
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

from idconfig.config import Config

try:
    import anthropic
    ANTHROPIC_AVAILABLE = True
except ImportError:
    ANTHROPIC_AVAILABLE = False
    logger.warning("anthropic库未安装，Claude模型不可用")

try:
    import google.generativeai as genai
    GOOGLE_AVAILABLE = True
except ImportError:
    GOOGLE_AVAILABLE = False
    logger.warning("google-generativeai库未安装，Gemini模型不可用")

try:
    from tencentcloud.common import credential
    from tencentcloud.common.profile.client_profile import ClientProfile
    from tencentcloud.common.profile.http_profile import HttpProfile
    from tencentcloud.hunyuan.v20230901 import hunyuan_client, models
    TENCENT_AVAILABLE = True
except ImportError:
    TENCENT_AVAILABLE = False
    logger.warning("tencentcloud-sdk-python库未安装，腾讯混元模型不可用")

class LLMService:
    def __init__(self):
        self.config = Config()
        self.setup_openai()
    
    def setup_openai(self):
        """设置OpenAI客户端"""
        if self.config.OPENAI_API_KEY:
            openai.api_key = self.config.OPENAI_API_KEY
            if self.config.OPENAI_BASE_URL:
                openai.base_url = self.config.OPENAI_BASE_URL
            logger.info("OpenAI客户端配置完成")
    
    def call_openai(self, messages: List[Dict[str, str]], temperature: float = 0.7) -> str:
        """调用OpenAI API"""
        try:
            if not self.config.OPENAI_API_KEY:
                logger.error("OpenAI API密钥未配置")
                return "抱歉，大模型服务暂时不可用。"
            
            client = openai.OpenAI(
                api_key=self.config.OPENAI_API_KEY,
                base_url=self.config.OPENAI_BASE_URL
            )
            
            response = client.chat.completions.create(
                model=self.config.MODEL_NAME,
                messages=messages,
                temperature=temperature,
                max_tokens=2000
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"调用OpenAI API失败: {e}")
            return "抱歉，处理您的请求时出现了错误。"
    
    def call_zhipu(self, messages: List[Dict[str, str]], temperature: float = 0.7) -> str:
        """调用智谱AI API"""
        try:
            if not self.config.ZHIPU_API_KEY:
                logger.error("智谱AI API密钥未配置")
                return "抱歉，智谱AI服务暂时不可用。"
            
            url = "https://open.bigmodel.cn/api/paas/v4/chat/completions"
            headers = {
                "Authorization": f"Bearer {self.config.ZHIPU_API_KEY}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": self.config.ZHIPU_MODEL,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": 2000
            }
            
            response = requests.post(url, headers=headers, json=data, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            return result["choices"][0]["message"]["content"]
            
        except Exception as e:
            logger.error(f"调用智谱AI API失败: {e}")
            return "抱歉，处理您的请求时出现了错误。"
    
    def call_qwen(self, messages: List[Dict[str, str]], temperature: float = 0.7) -> str:
        """调用通义千问API"""
        try:
            if not self.config.QWEN_API_KEY:
                logger.error("通义千问API密钥未配置")
                return "抱歉，通义千问服务暂时不可用。"

            # 这里需要根据实际的通义千问API接口进行调整
            url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
            headers = {
                "Authorization": f"Bearer {self.config.QWEN_API_KEY}",
                "Content-Type": "application/json"
            }

            # 将messages转换为通义千问格式
            prompt = self._convert_messages_to_prompt(messages)

            data = {
                "model": self.config.QWEN_MODEL,
                "input": {
                    "prompt": prompt
                },
                "parameters": {
                    "temperature": temperature,
                    "max_tokens": 2000
                }
            }

            response = requests.post(url, headers=headers, json=data, timeout=30)
            response.raise_for_status()

            result = response.json()
            return result["output"]["text"]

        except Exception as e:
            logger.error(f"调用通义千问API失败: {e}")
            return "抱歉，处理您的请求时出现了错误。"

    def call_siliconflow(self, messages: List[Dict[str, str]], temperature: float = 0.7, model: str = None) -> str:
        """调用硅基流动API"""
        try:
            if not self.config.SILICONFLOW_API_KEY:
                logger.error("硅基流动API密钥未配置")
                return "抱歉，硅基流动服务暂时不可用。"

            url = "https://api.siliconflow.cn/v1/chat/completions"
            headers = {
                "Authorization": f"Bearer {self.config.SILICONFLOW_API_KEY}",
                "Content-Type": "application/json"
            }

            data = {
                "model": model or self.config.SILICONFLOW_MODEL,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": 2000,
                "stream": False
            }

            response = requests.post(url, headers=headers, json=data, timeout=60)
            response.raise_for_status()

            result = response.json()
            return result["choices"][0]["message"]["content"]

        except Exception as e:
            logger.error(f"调用硅基流动API失败: {e}")
            return "抱歉，处理您的请求时出现了错误。"

    def call_siliconflow2(self, messages: List[Dict[str, str]], temperature: float = 0.7, model: str = None) -> str:
        """调用硅基流动2 API (额外的硅基流动配置)"""
        try:
            if not self.config.SILICONFLOW2_API_KEY:
                logger.error("硅基流动2 API密钥未配置")
                return "抱歉，硅基流动2服务暂时不可用。"

            url = f"{self.config.SILICONFLOW2_BASE_URL}/chat/completions"
            headers = {
                "Authorization": f"Bearer {self.config.SILICONFLOW2_API_KEY}",
                "Content-Type": "application/json"
            }

            data = {
                "model": model or self.config.SILICONFLOW2_MODEL,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": 2000,
                "stream": False
            }

            response = requests.post(url, headers=headers, json=data, timeout=60)
            response.raise_for_status()

            result = response.json()
            return result["choices"][0]["message"]["content"]

        except Exception as e:
            logger.error(f"调用硅基流动2 API失败: {e}")
            return "抱歉，处理您的请求时出现了错误。"

    def call_siliconflow2_vision(self, image_base64: str, prompt: str = "请详细描述这张图像的内容", temperature: float = 0.7, model: str = None) -> str:
        """调用硅基流动2的多模态视觉API分析图像"""
        try:
            if not self.config.SILICONFLOW2_API_KEY:
                logger.error("硅基流动2 API密钥未配置")
                return "抱歉，硅基流动2服务暂时不可用。"

            url = f"{self.config.SILICONFLOW2_BASE_URL}/chat/completions"
            headers = {
                "Authorization": f"Bearer {self.config.SILICONFLOW2_API_KEY}",
                "Content-Type": "application/json"
            }

            # 构建多模态消息
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": prompt
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{image_base64}"
                            }
                        }
                    ]
                }
            ]

            data = {
                "model": model or self.config.SILICONFLOW2_MODEL,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": 2000,
                "stream": False
            }

            response = requests.post(url, headers=headers, json=data, timeout=60)
            response.raise_for_status()

            result = response.json()
            return result["choices"][0]["message"]["content"]

        except Exception as e:
            logger.error(f"调用硅基流动2视觉API失败: {e}")
            return "抱歉，图像分析时出现了错误。"

    def call_siliconflow2_vision_batch(self, images_data: List[Dict[str, Any]],
                                     prompt: str = "请详细描述这些图像的内容",
                                     temperature: float = 0.7,
                                     model: str = None) -> Dict[str, Any]:
        """一次调用硅基流动2多模态API处理多个图像

        Args:
            images_data: 图像数据列表，每个元素包含:
                - image_base64: base64编码的图像
                - image_id: 图像唯一标识
                - context_info: 上下文信息（可选）
            prompt: 分析提示词
            temperature: 温度参数
            model: 模型名称

        Returns:
            Dict: 包含批量处理结果的字典
        """
        try:
            if not self.config.SILICONFLOW2_API_KEY:
                logger.error("硅基流动2 API密钥未配置")
                return {"success": False, "error": "硅基流动2服务暂时不可用"}

            if not images_data:
                return {"success": False, "error": "没有提供图像数据"}

            url = f"{self.config.SILICONFLOW2_BASE_URL}/chat/completions"
            headers = {
                "Authorization": f"Bearer {self.config.SILICONFLOW2_API_KEY}",
                "Content-Type": "application/json"
            }

            # 构建增强的批量提示词
            enhanced_prompt = self._build_batch_prompt(prompt, images_data)

            # 构建包含多个图像的消息内容
            content = [
                {
                    "type": "text",
                    "text": enhanced_prompt
                }
            ]

            # 添加所有图像
            for i, img_data in enumerate(images_data):
                image_base64 = img_data.get("image_base64", "")
                if image_base64:
                    content.append({
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{image_base64}"
                        }
                    })

            messages = [
                {
                    "role": "user",
                    "content": content
                }
            ]

            data = {
                "model": model or self.config.SILICONFLOW2_MODEL,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": 4000,  # 增加token限制以处理多图像
                "stream": False
            }

            response = requests.post(url, headers=headers, json=data, timeout=120)  # 增加超时时间
            response.raise_for_status()

            result = response.json()
            batch_description = result["choices"][0]["message"]["content"]

            # 解析批量结果
            parsed_results = self._parse_batch_results(batch_description, images_data)

            return {
                "success": True,
                "results": parsed_results,
                "raw_response": batch_description
            }

        except Exception as e:
            logger.error(f"调用硅基流动2批量视觉API失败: {e}")
            return {"success": False, "error": str(e)}

    def _build_batch_prompt(self, base_prompt: str, images_data: List[Dict[str, Any]]) -> str:
        """构建批量处理的增强提示词"""
        try:
            enhanced_prompt = base_prompt + "\n\n"

            # 添加批量处理指导
            enhanced_prompt += f"我将为您分析 {len(images_data)} 张图像。请按照以下要求：\n"
            enhanced_prompt += "1. 为每张图像生成详细的描述\n"
            enhanced_prompt += "2. 重点关注金融相关的内容，如图表、数据、趋势等\n"
            enhanced_prompt += "3. 如果图像包含文字，请提取关键信息\n"
            enhanced_prompt += "4. 请严格按照图像顺序，为每张图像生成独立的描述\n"
            enhanced_prompt += "5. 每个描述应该包含图像类型、主要内容、数据特征等\n\n"

            # 添加上下文信息
            for idx, img_data in enumerate(images_data):
                context_info = img_data.get("context_info", {})
                if context_info:
                    enhanced_prompt += f"图像 {idx+1} 的上下文信息：\n"
                    if context_info.get("page_text"):
                        page_text = context_info["page_text"][:200]  # 限制长度
                        enhanced_prompt += f"  页面文本：{page_text}...\n"
                    if context_info.get("pdf_name"):
                        enhanced_prompt += f"  来源文档：{context_info['pdf_name']}\n"
                    if context_info.get("page_number"):
                        enhanced_prompt += f"  页码：第{context_info['page_number']}页\n"
                    enhanced_prompt += "\n"

            # 强化输出格式要求
            enhanced_prompt += "【重要】请严格按照以下格式输出，每张图像必须有对应的描述：\n\n"

            for i in range(len(images_data)):
                enhanced_prompt += f"图像{i+1}：[请在此处写入第{i+1}张图像的详细描述]\n"

            enhanced_prompt += f"\n请确保：\n"
            enhanced_prompt += f"- 必须包含所有 {len(images_data)} 张图像的描述\n"
            enhanced_prompt += f"- 严格按照 '图像X：描述内容' 的格式\n"
            enhanced_prompt += f"- 每个描述要详细且专业\n"
            enhanced_prompt += f"- 不要遗漏任何一张图像\n"
            enhanced_prompt += f"- 按照图像出现的顺序编号\n"

            return enhanced_prompt

        except Exception as e:
            logger.warning(f"构建批量提示词失败: {e}")
            return base_prompt

    def _parse_batch_results(self, batch_description: str, images_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """解析批量描述结果并分配给各个图像"""
        try:
            results = []

            # 记录原始响应用于调试
            logger.debug(f"原始批量响应: {batch_description[:500]}...")

            # 多种解析策略
            # 策略1: 按照标准格式解析
            results = self._parse_standard_format(batch_description, images_data)

            # 策略2: 如果标准格式解析失败，尝试按段落解析
            if len(results) < len(images_data):
                logger.info("标准格式解析不完整，尝试段落解析")
                results = self._parse_paragraph_format(batch_description, images_data)

            # 策略3: 如果还是不完整，尝试按句子分割
            if len(results) < len(images_data):
                logger.info("段落解析不完整，尝试句子分割")
                results = self._parse_sentence_format(batch_description, images_data)

            # 最后的备用方案：平均分配描述
            if len(results) < len(images_data):
                logger.warning(f"所有解析策略都不完整，期望 {len(images_data)} 个，实际 {len(results)} 个，使用备用方案")
                results = self._fallback_parse(batch_description, images_data)

            return results[:len(images_data)]  # 确保不超过输入数量

        except Exception as e:
            logger.error(f"解析批量描述失败: {e}")
            # 返回错误结果
            return [{"image_id": item.get("image_id", f"image_{i+1}"),
                    "description": f"描述解析失败: {str(e)}",
                    "success": False,
                    "error": str(e)} for i, item in enumerate(images_data)]

    def _parse_standard_format(self, batch_description: str, images_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """按照标准格式解析（图像1：描述）"""
        results = []
        lines = batch_description.split('\n')
        current_image_index = 0
        current_description = ""

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 检查是否是新图像的开始 - 更宽松的匹配
            image_pattern_found = False
            new_index = None

            # 多种可能的格式
            patterns = [
                f"图像{current_image_index + 1}",
                f"Image {current_image_index + 1}",
                f"图片{current_image_index + 1}",
                f"{current_image_index + 1}.",
                f"第{current_image_index + 1}张",
                f"图{current_image_index + 1}",
                f"({current_image_index + 1})"
            ]

            for pattern in patterns:
                if pattern in line:
                    image_pattern_found = True
                    new_index = current_image_index + 1
                    break

            # 也检查是否包含数字模式
            if not image_pattern_found:
                import re
                number_match = re.search(r'(\d+)[：:.]', line)
                if number_match:
                    num = int(number_match.group(1))
                    if num == current_image_index + 1:
                        image_pattern_found = True
                        new_index = num

            if image_pattern_found and new_index:
                # 保存前一个图像的描述
                if current_description and current_image_index > 0:
                    if current_image_index - 1 < len(images_data):
                        results.append({
                            "image_id": images_data[current_image_index - 1].get("image_id", f"image_{current_image_index}"),
                            "description": current_description.strip(),
                            "success": True
                        })

                # 开始新图像的描述
                if '：' in line:
                    current_description = line.split('：', 1)[-1].strip()
                elif ':' in line:
                    current_description = line.split(':', 1)[-1].strip()
                else:
                    current_description = line
                current_image_index = new_index
            else:
                # 继续当前图像的描述
                if current_description:
                    current_description += " " + line
                else:
                    current_description = line

        # 保存最后一个图像的描述
        if current_description and current_image_index > 0:
            if current_image_index - 1 < len(images_data):
                results.append({
                    "image_id": images_data[current_image_index - 1].get("image_id", f"image_{current_image_index}"),
                    "description": current_description.strip(),
                    "success": True
                })

        return results

    def _parse_paragraph_format(self, batch_description: str, images_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """按段落解析描述"""
        results = []

        # 按双换行符分割段落
        paragraphs = [p.strip() for p in batch_description.split('\n\n') if p.strip()]

        # 如果段落数量不够，按单换行符分割
        if len(paragraphs) < len(images_data):
            paragraphs = [p.strip() for p in batch_description.split('\n') if p.strip() and len(p) > 20]

        # 为每个图像分配段落
        for i, img_data in enumerate(images_data):
            if i < len(paragraphs):
                description = paragraphs[i]
                # 清理描述开头的编号
                import re
                description = re.sub(r'^[\d\s\.\：:图像图片第张\(\)]+', '', description).strip()

                results.append({
                    "image_id": img_data.get("image_id", f"image_{i+1}"),
                    "description": description,
                    "success": True,
                    "parse_method": "paragraph"
                })

        return results

    def _parse_sentence_format(self, batch_description: str, images_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """按句子分割解析"""
        results = []

        # 按句号分割
        sentences = [s.strip() for s in batch_description.split('。') if s.strip() and len(s) > 15]

        # 如果句子不够，按其他标点分割
        if len(sentences) < len(images_data):
            import re
            sentences = re.split(r'[。！？；\n]', batch_description)
            sentences = [s.strip() for s in sentences if s.strip() and len(s) > 15]

        # 为每个图像分配句子
        for i, img_data in enumerate(images_data):
            if i < len(sentences):
                description = sentences[i]
                # 清理描述
                import re
                description = re.sub(r'^[\d\s\.\：:图像图片第张\(\)]+', '', description).strip()

                results.append({
                    "image_id": img_data.get("image_id", f"image_{i+1}"),
                    "description": description,
                    "success": True,
                    "parse_method": "sentence"
                })

        return results

    def _fallback_parse(self, batch_description: str, images_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """备用解析方案：平均分配描述"""
        results = []

        # 清理描述
        clean_description = batch_description.strip()

        # 尝试智能分割
        words = clean_description.split()
        words_per_image = max(1, len(words) // len(images_data))

        for i, img_data in enumerate(images_data):
            start_idx = i * words_per_image
            end_idx = (i + 1) * words_per_image if i < len(images_data) - 1 else len(words)

            if start_idx < len(words):
                description = ' '.join(words[start_idx:end_idx])
                if not description.strip():
                    description = f"图像{i+1}的描述：{clean_description[:200]}..."
            else:
                description = f"图像{i+1}的描述：{clean_description[:200]}..."

            results.append({
                "image_id": img_data.get("image_id", f"image_{i+1}"),
                "description": description,
                "success": True,
                "parse_method": "fallback"
            })

        return results

    def call_baidu(self, messages: List[Dict[str, str]], temperature: float = 0.7) -> str:
        """调用百度文心一言API"""
        try:
            if not self.config.BAIDU_API_KEY or not self.config.BAIDU_SECRET_KEY:
                logger.error("百度API密钥未配置")
                return "抱歉，百度文心一言服务暂时不可用。"

            # 获取access_token
            access_token = self._get_baidu_access_token()
            if not access_token:
                return "抱歉，获取百度访问令牌失败。"

            url = f"{self.config.BAIDU_BASE_URL}/chat/completions_pro?access_token={access_token}"
            headers = {
                "Content-Type": "application/json"
            }

            data = {
                "messages": messages,
                "temperature": temperature,
                "max_output_tokens": 2000
            }

            response = requests.post(url, headers=headers, json=data, timeout=30)
            response.raise_for_status()

            result = response.json()
            if "result" in result:
                return result["result"]
            else:
                logger.error(f"百度API返回格式错误: {result}")
                return "抱歉，处理您的请求时出现了错误。"

        except Exception as e:
            logger.error(f"调用百度文心一言API失败: {e}")
            return "抱歉，处理您的请求时出现了错误。"

    def _get_baidu_access_token(self) -> str:
        """获取百度access_token"""
        try:
            url = "https://aip.baidubce.com/oauth/2.0/token"
            params = {
                "grant_type": "client_credentials",
                "client_id": self.config.BAIDU_API_KEY,
                "client_secret": self.config.BAIDU_SECRET_KEY
            }

            response = requests.post(url, params=params, timeout=10)
            response.raise_for_status()

            result = response.json()
            return result.get("access_token", "")

        except Exception as e:
            logger.error(f"获取百度access_token失败: {e}")
            return ""

    def call_moonshot(self, messages: List[Dict[str, str]], temperature: float = 0.7) -> str:
        """调用Moonshot AI (Kimi) API"""
        try:
            if not self.config.MOONSHOT_API_KEY:
                logger.error("Moonshot API密钥未配置")
                return "抱歉，Moonshot AI服务暂时不可用。"

            url = f"{self.config.MOONSHOT_BASE_URL}/chat/completions"
            headers = {
                "Authorization": f"Bearer {self.config.MOONSHOT_API_KEY}",
                "Content-Type": "application/json"
            }

            data = {
                "model": self.config.MOONSHOT_MODEL,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": 2000
            }

            response = requests.post(url, headers=headers, json=data, timeout=30)
            response.raise_for_status()

            result = response.json()
            return result["choices"][0]["message"]["content"]

        except Exception as e:
            logger.error(f"调用Moonshot AI API失败: {e}")
            return "抱歉，处理您的请求时出现了错误。"

    def call_deepseek(self, messages: List[Dict[str, str]], temperature: float = 0.7) -> str:
        """调用DeepSeek API"""
        try:
            if not self.config.DEEPSEEK_API_KEY:
                logger.error("DeepSeek API密钥未配置")
                return "抱歉，DeepSeek服务暂时不可用。"

            url = f"{self.config.DEEPSEEK_BASE_URL}/chat/completions"
            headers = {
                "Authorization": f"Bearer {self.config.DEEPSEEK_API_KEY}",
                "Content-Type": "application/json"
            }

            data = {
                "model": self.config.DEEPSEEK_MODEL,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": 2000
            }

            response = requests.post(url, headers=headers, json=data, timeout=30)
            response.raise_for_status()

            result = response.json()
            return result["choices"][0]["message"]["content"]

        except Exception as e:
            logger.error(f"调用DeepSeek API失败: {e}")
            return "抱歉，处理您的请求时出现了错误。"

    def call_doubao(self, messages: List[Dict[str, str]], temperature: float = 0.7) -> str:
        """调用字节豆包API"""
        try:
            if not self.config.DOUBAO_API_KEY:
                logger.error("豆包API密钥未配置")
                return "抱歉，豆包服务暂时不可用。"

            url = f"{self.config.DOUBAO_BASE_URL}/chat/completions"
            headers = {
                "Authorization": f"Bearer {self.config.DOUBAO_API_KEY}",
                "Content-Type": "application/json"
            }

            data = {
                "model": self.config.DOUBAO_MODEL,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": 2000
            }

            response = requests.post(url, headers=headers, json=data, timeout=30)
            response.raise_for_status()

            result = response.json()
            return result["choices"][0]["message"]["content"]

        except Exception as e:
            logger.error(f"调用豆包API失败: {e}")
            return "抱歉，处理您的请求时出现了错误。"

    def call_claude(self, messages: List[Dict[str, str]], temperature: float = 0.7) -> str:
        """调用Anthropic Claude API"""
        try:
            if not ANTHROPIC_AVAILABLE:
                return "抱歉，Claude服务需要安装anthropic库。"

            if not self.config.ANTHROPIC_API_KEY:
                logger.error("Anthropic API密钥未配置")
                return "抱歉，Claude服务暂时不可用。"

            client = anthropic.Anthropic(api_key=self.config.ANTHROPIC_API_KEY)

            # 转换消息格式
            claude_messages = []
            system_message = ""

            for msg in messages:
                if msg["role"] == "system":
                    system_message = msg["content"]
                else:
                    claude_messages.append({
                        "role": msg["role"],
                        "content": msg["content"]
                    })

            response = client.messages.create(
                model=self.config.ANTHROPIC_MODEL,
                max_tokens=2000,
                temperature=temperature,
                system=system_message,
                messages=claude_messages
            )

            return response.content[0].text

        except Exception as e:
            logger.error(f"调用Claude API失败: {e}")
            return "抱歉，处理您的请求时出现了错误。"

    def call_gemini(self, messages: List[Dict[str, str]], temperature: float = 0.7) -> str:
        """调用Google Gemini API"""
        try:
            if not GOOGLE_AVAILABLE:
                return "抱歉，Gemini服务需要安装google-generativeai库。"

            if not self.config.GOOGLE_API_KEY:
                logger.error("Google API密钥未配置")
                return "抱歉，Gemini服务暂时不可用。"

            genai.configure(api_key=self.config.GOOGLE_API_KEY)
            model = genai.GenerativeModel(self.config.GOOGLE_MODEL)

            # 转换消息格式为单个提示
            prompt_parts = []
            for msg in messages:
                role = msg["role"]
                content = msg["content"]
                if role == "system":
                    prompt_parts.append(f"系统指令: {content}")
                elif role == "user":
                    prompt_parts.append(f"用户: {content}")
                elif role == "assistant":
                    prompt_parts.append(f"助手: {content}")

            prompt = "\n".join(prompt_parts)

            response = model.generate_content(
                prompt,
                generation_config=genai.types.GenerationConfig(
                    temperature=temperature,
                    max_output_tokens=2000
                )
            )

            return response.text

        except Exception as e:
            logger.error(f"调用Gemini API失败: {e}")
            return "抱歉，处理您的请求时出现了错误。"
    
    def _convert_messages_to_prompt(self, messages: List[Dict[str, str]]) -> str:
        """将消息列表转换为提示文本"""
        prompt_parts = []
        for message in messages:
            role = message["role"]
            content = message["content"]
            if role == "system":
                prompt_parts.append(f"系统: {content}")
            elif role == "user":
                prompt_parts.append(f"用户: {content}")
            elif role == "assistant":
                prompt_parts.append(f"助手: {content}")
        
        return "\n".join(prompt_parts)
    
    def generate_response(self, messages: List[Dict[str, str]],
                         model_provider: str = "openai",
                         temperature: float = 0.7,
                         model: str = None) -> str:
        """生成回复"""
        try:
            provider = model_provider.lower()

            if provider == "openai":
                return self.call_openai(messages, temperature)
            elif provider == "zhipu":
                return self.call_zhipu(messages, temperature)
            elif provider == "qwen":
                return self.call_qwen(messages, temperature)
            elif provider == "siliconflow":
                if model:
                    return self.call_siliconflow(messages, temperature, model)
                else:
                    return self.call_siliconflow(messages, temperature)
            elif provider == "siliconflow2":
                if model:
                    return self.call_siliconflow2(messages, temperature, model)
                else:
                    return self.call_siliconflow2(messages, temperature)
            elif provider == "baidu":
                return self.call_baidu(messages, temperature)
            elif provider == "moonshot":
                return self.call_moonshot(messages, temperature)
            elif provider == "deepseek":
                return self.call_deepseek(messages, temperature)
            elif provider == "doubao":
                return self.call_doubao(messages, temperature)
            elif provider == "claude":
                return self.call_claude(messages, temperature)
            elif provider == "gemini":
                return self.call_gemini(messages, temperature)
            else:
                logger.error(f"不支持的模型提供商: {model_provider}")
                return "抱歉，不支持的模型提供商。"

        except Exception as e:
            logger.error(f"生成回复失败: {e}")
            return "抱歉，生成回复时出现了错误。"
    
    def create_financial_prompt(self, user_query: str, context: Dict[str, Any]) -> List[Dict[str, str]]:
        """创建金融对话的提示"""
        # 构建系统提示
        has_images = context.get("images") and len(context.get("images", [])) > 0

        system_prompt = """你是一个专业的金融助手，具有丰富的金融知识和经验。请根据提供的上下文信息回答用户的问题。

回答要求：
1. 准确、专业、客观
2. 基于提供的上下文信息
3. 如果上下文信息不足，请明确说明
4. 使用简洁明了的语言
5. 必要时提供相关的金融术语解释"""

        if has_images:
            system_prompt += """
6. 特别注意：当提供了相关图片信息时，请充分利用这些图片内容来增强回答
7. 如果用户询问图表、数据可视化、趋势分析等内容，请重点参考图片信息
8. 在回答中可以明确提及图片中的具体内容，如"根据第X页的图表显示..."
9. 对于图片中的数据和趋势，请提供专业的分析和解读"""

        system_prompt += "\n\n请始终保持专业性和准确性。"""
        
        # 构建上下文信息
        context_text = ""
        
        # 添加知识库信息
        if context.get("knowledge"):
            context_text += "相关专业知识：\n"
            for i, item in enumerate(context["knowledge"][:3], 1):  # 只取前3条
                context_text += f"{i}. {item['content']} (来源: {item['source']})\n"
            context_text += "\n"
        
        # 添加历史对话信息
        if context.get("history"):
            context_text += "相关历史对话：\n"
            for i, item in enumerate(context["history"][:2], 1):  # 只取前2条
                context_text += f"{i}. 用户问: {item['user_query']}\n   回答: {item['assistant_response']}\n"
            context_text += "\n"

        # 添加相关图片信息
        if context.get("images"):
            context_text += "📊 相关图片信息：\n"
            for i, img in enumerate(context["images"][:3], 1):  # 只取前3张
                context_text += f"\n图片 {i}:\n"
                context_text += f"  📄 来源文档: {img.get('pdf_name', '未知')}\n"
                context_text += f"  📖 页码: 第{img.get('page_number', 0)}页\n"
                context_text += f"  🏷️ 图片类型: {img.get('image_type', '未知')}\n"
                context_text += f"  📝 详细描述: {img.get('description', '无描述')}\n"
                context_text += f"  📏 图片尺寸: {img.get('width', 0)} × {img.get('height', 0)}\n"
                context_text += f"  🎯 相关度: {(img.get('final_score', 0) * 100):.1f}%\n"

                # 如果有提取的文本，也包含进来
                if img.get('extracted_text'):
                    context_text += f"  📋 图片中的文本: {img.get('extracted_text')}\n"

            context_text += "\n💡 图片分析指导：\n"
            context_text += "- 这些图片与用户的问题高度相关，请在回答中充分利用\n"
            context_text += "- 如果用户询问数据、趋势、图表等内容，请重点参考图片信息\n"
            context_text += "- 可以在回答中明确引用图片，如\"根据第X页的图表显示...\"\n"
            context_text += "- 对图片中的数据和趋势提供专业的金融分析和解读\n"

            # 添加针对性的分析指导
            specific_guidance = self._analyze_query_image_relevance(user_query, context.get("images", []))
            context_text += specific_guidance + "\n"
        
        # 构建用户消息
        user_message = f"""上下文信息：
{context_text}

用户问题：{user_query}

请基于上述上下文信息回答用户的问题。"""
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_message}
        ]
        
        return messages

    def _analyze_query_image_relevance(self, query: str, images: List[Dict[str, Any]]) -> str:
        """分析查询与图片的相关性，生成针对性的指导"""
        if not images:
            return ""

        query_lower = query.lower()

        # 图表相关关键词
        chart_keywords = ["图表", "图", "数据", "趋势", "分析", "统计", "报表", "可视化", "柱状图", "折线图", "饼图"]
        # 财务相关关键词
        financial_keywords = ["收入", "利润", "营收", "增长", "市场", "财务", "业绩", "指标", "比率"]
        # 时间相关关键词
        time_keywords = ["年", "月", "季度", "期间", "历史", "变化", "对比"]

        has_chart_query = any(keyword in query_lower for keyword in chart_keywords)
        has_financial_query = any(keyword in query_lower for keyword in financial_keywords)
        has_time_query = any(keyword in query_lower for keyword in time_keywords)

        guidance = "\n🎯 针对性分析指导：\n"

        if has_chart_query:
            guidance += "- 用户询问图表相关内容，请重点分析图片中的数据表现形式\n"
            guidance += "- 描述图表类型、数据趋势、关键数据点\n"

        if has_financial_query:
            guidance += "- 用户关注财务指标，请从图片中提取相关财务数据\n"
            guidance += "- 分析财务表现、增长情况、市场地位等\n"

        if has_time_query:
            guidance += "- 用户询问时间相关变化，请关注图片中的时间序列数据\n"
            guidance += "- 分析历史趋势、周期性变化、未来预测\n"

        # 分析图片类型分布
        image_types = [img.get('image_type', '') for img in images]
        if any('图表' in img_type or '图' in img_type for img_type in image_types):
            guidance += "- 发现图表类图片，请详细解读图表内容和数据含义\n"

        return guidance
    
    def get_available_models(self) -> List[str]:
        """获取可用的模型列表"""
        available_models = []

        if self.config.OPENAI_API_KEY:
            available_models.append("openai")

        if self.config.ZHIPU_API_KEY:
            available_models.append("zhipu")

        if self.config.QWEN_API_KEY:
            available_models.append("qwen")

        if self.config.SILICONFLOW_API_KEY:
            available_models.append("siliconflow")

        if self.config.SILICONFLOW2_API_KEY:
            available_models.append("siliconflow2")

        if self.config.BAIDU_API_KEY and self.config.BAIDU_SECRET_KEY:
            available_models.append("baidu")

        if self.config.MOONSHOT_API_KEY:
            available_models.append("moonshot")

        if self.config.DEEPSEEK_API_KEY:
            available_models.append("deepseek")

        if self.config.DOUBAO_API_KEY:
            available_models.append("doubao")

        if self.config.ANTHROPIC_API_KEY and ANTHROPIC_AVAILABLE:
            available_models.append("claude")

        if self.config.GOOGLE_API_KEY and GOOGLE_AVAILABLE:
            available_models.append("gemini")

        return available_models

    def get_siliconflow_models(self) -> List[str]:
        """获取硅基流动支持的模型列表"""
        return [
            "Qwen/Qwen2.5-7B-Instruct",
            "Qwen/Qwen2.5-14B-Instruct",
            "Qwen/Qwen2.5-32B-Instruct",
            "Qwen/Qwen2.5-72B-Instruct",
            "meta-llama/Meta-Llama-3.1-8B-Instruct",
            "meta-llama/Meta-Llama-3.1-70B-Instruct",
            "meta-llama/Meta-Llama-3.1-405B-Instruct",
            "deepseek-ai/DeepSeek-V2.5",
            "01-ai/Yi-1.5-9B-Chat-16K",
            "01-ai/Yi-1.5-34B-Chat-16K",
            "google/gemma-2-9b-it",
            "google/gemma-2-27b-it",
            "mistralai/Mistral-7B-Instruct-v0.3",
            "mistralai/Mixtral-8x7B-Instruct-v0.1",
            "mistralai/Mixtral-8x22B-Instruct-v0.1"
        ]

    def get_siliconflow2_models(self) -> List[str]:
        """获取硅基流动2支持的模型列表"""
        return [
            "Qwen/Qwen2.5-72B-Instruct",
            "Qwen/Qwen2.5-32B-Instruct",
            "meta-llama/Meta-Llama-3.1-70B-Instruct",
            "meta-llama/Meta-Llama-3.1-405B-Instruct",
            "deepseek-ai/DeepSeek-V2.5",
            "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B",
            "mistralai/Mixtral-8x22B-Instruct-v0.1",
            "01-ai/Yi-1.5-34B-Chat-16K",
            "google/gemma-2-27b-it"
        ]

    def get_model_info(self) -> Dict[str, Dict[str, Any]]:
        """获取所有模型的详细信息"""
        model_info = {
            "openai": {
                "name": "OpenAI GPT",
                "description": "OpenAI的GPT系列模型",
                "models": ["gpt-3.5-turbo", "gpt-4", "gpt-4-turbo"],
                "available": bool(self.config.OPENAI_API_KEY)
            },
            "zhipu": {
                "name": "智谱AI",
                "description": "智谱AI的GLM系列模型",
                "models": ["glm-4", "glm-3-turbo"],
                "default_model": self.config.ZHIPU_MODEL,
                "available": bool(self.config.ZHIPU_API_KEY)
            },
            "qwen": {
                "name": "通义千问",
                "description": "阿里云的通义千问系列模型",
                "models": ["qwen-turbo", "qwen-plus", "qwen-max"],
                "default_model": self.config.QWEN_MODEL,
                "available": bool(self.config.QWEN_API_KEY)
            },
            "siliconflow": {
                "name": "硅基流动",
                "description": "硅基流动平台的多种开源模型",
                "models": self.get_siliconflow_models(),
                "default_model": self.config.SILICONFLOW_MODEL,
                "available": bool(self.config.SILICONFLOW_API_KEY)
            },
            "siliconflow2": {
                "name": "硅基流动2",
                "description": "硅基流动平台的高性能模型配置",
                "models": self.get_siliconflow2_models(),
                "default_model": self.config.SILICONFLOW2_MODEL,
                "available": bool(self.config.SILICONFLOW2_API_KEY)
            },
            "baidu": {
                "name": "百度文心一言",
                "description": "百度的文心一言系列模型",
                "models": ["ernie-bot", "ernie-bot-turbo"],
                "default_model": self.config.BAIDU_MODEL,
                "available": bool(self.config.BAIDU_API_KEY and self.config.BAIDU_SECRET_KEY)
            },
            "moonshot": {
                "name": "Moonshot AI (Kimi)",
                "description": "月之暗面的Kimi系列模型",
                "models": ["moonshot-v1-8k", "moonshot-v1-32k", "moonshot-v1-128k"],
                "default_model": self.config.MOONSHOT_MODEL,
                "available": bool(self.config.MOONSHOT_API_KEY)
            },
            "deepseek": {
                "name": "DeepSeek",
                "description": "深度求索的DeepSeek系列模型",
                "models": ["deepseek-chat", "deepseek-coder"],
                "default_model": self.config.DEEPSEEK_MODEL,
                "available": bool(self.config.DEEPSEEK_API_KEY)
            },
            "doubao": {
                "name": "字节豆包",
                "description": "字节跳动的豆包系列模型",
                "models": ["doubao-pro", "doubao-lite"],
                "default_model": self.config.DOUBAO_MODEL,
                "available": bool(self.config.DOUBAO_API_KEY)
            },
            "claude": {
                "name": "Anthropic Claude",
                "description": "Anthropic的Claude系列模型",
                "models": ["claude-3-sonnet", "claude-3-opus", "claude-3-haiku"],
                "default_model": self.config.ANTHROPIC_MODEL,
                "available": bool(self.config.ANTHROPIC_API_KEY and ANTHROPIC_AVAILABLE)
            },
            "gemini": {
                "name": "Google Gemini",
                "description": "Google的Gemini系列模型",
                "models": ["gemini-pro", "gemini-pro-vision"],
                "default_model": self.config.GOOGLE_MODEL,
                "available": bool(self.config.GOOGLE_API_KEY and GOOGLE_AVAILABLE)
            }
        }

        return model_info
